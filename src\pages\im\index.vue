<template>
    <div>
        <button @click="openConversationList">打开会话列表</button>
        <button @click="openContact">打开联系人</button>
    </div>
</template>
<script setup>
import { onLoad, onShow } from "@dcloudio/uni-app"
import { TUILogin } from "@tencentcloud/tui-core"
import useStore from "@/store"


const { user } = useStore()

const openConversationList = () => {
    uni.navigateTo({ url: "/TUIKit/components/TUIConversation/index" })
}

const openContact = () => {
    uni.navigateTo({ url: "/TUIKit/components/TUIContact/index" })
}

onShow(async () => {
        console.log(user?.sigGen?.sdkAppId);
    const res = await TUILogin.login({
        SDKAppID:  user?.sigGen?.sdkAppId,
        userID: user?.sigGen?.identifier,
        userSig: user?.sigGen?.userSig,
        framework: "vue3"
    })
})

</script>
