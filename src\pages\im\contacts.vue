<!-- 通讯录 -->
<template>
    <CustomTabbar :tab-list="productTabList">
        <TUIContact />
    </CustomTabbar>
</template>

<script setup>
import CustomTabbar from "./customTabbar.vue"
import TUIContact from "@/TUIKit/components/TUIContact/index.vue"
import useStore from "@/store"
import { onLoad, onShow } from "@dcloudio/uni-app"
import { TUILogin } from "@tencentcloud/tui-core"
const { user } = useStore()

console.log(user,'123123123');

// tabbar
const productTabList = [
    {
        text: "消息",
        pagePath: "/pages/im/message",
        iconPath: "@nginx/home/<USER>/newsUnSelect.png",
        selectedIconPath: "/@nginx/workbench/dormManage/newsSelect.png"
    },
    {
        text: "通讯录",
        pagePath: "/pages/im/contacts",
        iconPath: "@nginx/home/<USER>/homeUnSelect.png",
        selectedIconPath: "@nginx/home/<USER>/homeSelect.png"
    }
]

onShow(async () => {
    console.log(user?.sigGen?.sdkAppId);
    
    const res = await TUILogin.login({
        SDKAppID: user?.sigGen?.sdkAppId,
        userID: user?.sigGen?.identifier,
        userSig: user?.sigGen?.userSig,
        framework: "vue3"
    })
})
</script>
